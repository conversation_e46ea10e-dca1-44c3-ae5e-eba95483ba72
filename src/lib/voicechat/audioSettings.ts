export interface AudioDevice {
	deviceId: string;
	label: string;
	kind: 'audioinput' | 'audiooutput';
	groupId: string;
}
export class AudioSettings {
	inputDevices: AudioDevice[];
	outputDevices: AudioDevice[];
	inputDeviceId: string;
	outputDeviceId: string;
	inputGain: number;
	outputGain: number;
	muted: boolean;
	sampleRate: number;
	bufferSize: number;

	constructor() {
		this.inputDevices = [];
		this.outputDevices = [];
		this.inputDeviceId = 'default';
		this.outputDeviceId = 'default';
		this.inputGain = 1.0;
		this.outputGain = 1.0;
		this.muted = false;
		this.sampleRate = 48000;
		this.bufferSize = 256;
	}

    	// TODO: store in local storage and/or svelte store
	loadFromJSON(json: string) {
		const data = JSON.parse(json);
		this.inputDevices = data.inputDevices;
		this.outputDevices = data.outputDevices;
		this.inputDeviceId = data.inputDeviceId;
		this.outputDeviceId = data.outputDeviceId;
		this.inputGain = data.inputGain;
		this.outputGain = data.outputGain;
		this.muted = data.muted;
		this.sampleRate = data.sampleRate;
		this.bufferSize = data.bufferSize;
	}

	toJSON() {
		return JSON.stringify(this);
	}

	public getInputDeviceId() {
		return this.inputDeviceId;
	}

	public setInputDeviceId(devId: string) {
		this.inputDeviceId = devId;
		//handleDeviceChange(devId);
	}
	public async initDevices() {
		await this.enumerateDevices();
		// Listen for device changes
		if (navigator.mediaDevices) {
			navigator.mediaDevices.addEventListener('devicechange', () => {
				this.enumerateDevices();
			});
		}
	}
	async enumerateDevices() {
		try {
			const devices = await navigator.mediaDevices.enumerateDevices();
			this.inputDevices = devices
				.filter((device) => device.kind === 'audioinput')
				.map((device) => ({
					deviceId: device.deviceId,
					label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
					kind: device.kind as 'audioinput' | 'audiooutput',
					groupId: device.groupId
				}));
			this.outputDevices = devices
				.filter((device) => device.kind === 'audiooutput')
				.map((device) => ({
					deviceId: device.deviceId,
					label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
					kind: device.kind as 'audioinput' | 'audiooutput',
					groupId: device.groupId
				}));
		} catch (error) {
			console.error('Failed to enumerate devices:', error);
			
		}
	}
}
