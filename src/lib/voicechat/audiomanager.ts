//import { AudioPlayer } from './audio-player';
import type { AudioDevice, AudioSettings, AudioMeterData } from './types';
import { arrayBufferToBase64 } from './util';

// handle input audio input and output
// handle socket.io connection
// handle device selection , volume control, muting, etc.
// handle meterings
//

export class AudioManager {
	// states:
	// uninitialized: nothing set up
	// initialized: context setup but no dataflow
	private inputInitialized: boolean = false;
	private outputInitialized: boolean = false;
	// started: dataflow established
	private inputStarted: boolean = false;
	private outputStarted: boolean = false;
	// stopped: dataflow suspended
	// streaming: hooked up server
	private isStreaming: boolean = false;

	//private audioContext: AudioContext | null = null;
	private inputStream: MediaStream | null = null;
	private inputNode: MediaStreamAudioSourceNode | null = null;
	private inputAnalyserNode: AnalyserNode | null = null;
	private gainNode: GainNode | null = null;
	private inputGainNode: GainNode | null = null;
	private outputgainNode: GainNode | null = null;

	private destinationNode: MediaStreamAudioDestinationNode | null = null;
	private animationFrame: number | null = null;

	// from voicechat.svelte
	private inputAudioContext: AudioContext | null = null;
	//private audioStream: MediaStream | null = null;
	private microphoneWorkletNode: AudioWorkletNode | null = null;
	private sourceNode: MediaStreamAudioSourceNode | null = null;
	//private audioPlayer: AudioPlayer | null = null;
	private sessionInitialized = false;
	private samplingRatio = 1;
	private TARGET_SAMPLE_RATE = 16000;
	isFirefox = navigator.userAgent.toLowerCase().includes('firefox');

	// from audio-player.ts
	private onAudioPlayedListeners: Array<any> = [];

	private outputAudioContext: AudioContext | null = null;
	private outputAnalyser: AnalyserNode | null = null;
	private playerWorkletNode: AudioWorkletNode | null = null;
	private monitorWorkletNode: AudioWorkletNode | null = null; // Replaces ScriptProcessorNode

	// need a callback method for status updates
	// is this in constructor or init method?
	// audio nodes:
	//  Are these separate audio graphs?
	//   inputNode -> gainNode -> microphoneWorkletNode -> analyserNode
	//   (FROM LLM) -> gainNode -> audioPlayerNode ->analyserNode

	// need constructor
	// need initInput method
	// need initOutput method
	// need method to enable streaming to/from LLM
	// this is independent of input/output so metering is visible even when not streaming
	// need method to disable streaming to/from LLM


	// TODO: store in local storage and/or svelte store


	private meterCallbacks: ((data: AudioMeterData) => void)[] = [];
	private deviceChangeCallbacks: ((devices: AudioDevice[]) => void)[] = [];

	//private statusCallback: ((status: string) => void) | null = null;
	inputDevices: AudioDevice[];
	outputDevices: AudioDevice[];
	inputDeviceId: string;
	outputDeviceId: string;
	inputGain: number;
	outputGain: number;
	muted: boolean;
	sampleRate: number;
	bufferSize: number;

	constructor() {
		this.inputDevices = [];
		this.outputDevices = [];
		this.inputDeviceId = 'default';
		this.outputDeviceId = 'default';
		this.inputGain = 1.0;
		this.outputGain = 1.0;
		this.muted = false;
		this.sampleRate = 48000;
		this.bufferSize = 256;
	}

	async initDevices() {
		try {
			const devices = await navigator.mediaDevices.enumerateDevices();
			this.inputDevices = devices
				.filter((device) => device.kind === 'audioinput')
				.map((device) => ({
					deviceId: device.deviceId,
					label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
					kind: device.kind as 'audioinput' | 'audiooutput',
					groupId: device.groupId
				}));
			this.outputDevices = devices
				.filter((device) => device.kind === 'audiooutput')
				.map((device) => ({
					deviceId: device.deviceId,
					label: device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`,
					kind: device.kind as 'audioinput' | 'audiooutput',
					groupId: device.groupId
				}));

			// Listen for device changes
			if (navigator.mediaDevices) {
				navigator.mediaDevices.addEventListener('devicechange', () => {
					this.initDevices();
				});
			}
		} catch (error) {
			console.error('Failed to enumerate devices:', error);
		}
	}

	public getInputDeviceId() {
		return this.inputDeviceId;
	}

	public setInputDeviceId(devId: string) {
		this.inputDeviceId = devId;
	}

	public getOutputDeviceId():string {
		return this.outputDeviceId;
	}

	public setOutputDeviceId(devId: string) {
		this.outputDeviceId = devId;
	}

	setMuted(muted: boolean): void {
		this.muted = muted;
		if (this.gainNode) {
			this.gainNode.gain.value = muted ? 0 : this.inputGain;
		}
	}
	public getMuted() {
		return this.muted;
	}
	setInputGain(gain: number): void {
		this.inputGain = Math.max(0, Math.min(2, gain));
		if (this.gainNode) {
			this.gainNode.gain.value = this.inputGain;
		}
	}
	getInputGain() {
		return this.inputGain;  // does this need to be tweaked like above?
	}	

	setOutputGain(gain: number): void {
		this.outputGain = Math.max(0, Math.min(2, gain));
	}
	getOutputGain() {
		return this.outputGain;  // does this need to be tweaked like above?
	}	
	


	getInputDevices(): AudioDevice[] {
		return this.inputDevices;
	}

	getOutputDevices(): AudioDevice[] {
		return this.outputDevices;
	}

	
	// ***************** OUTPUT ****************

	public async InitOutput() {
		try {
			this.outputAudioContext = new AudioContext({ sampleRate: 24000 });
			this.outputAnalyser = this.outputAudioContext!.createAnalyser();
			this.outputAnalyser.fftSize = 512;
			// Load both worklet modules
			const audioPlayerWorkletUrl = new URL(
				'./AudioPlayerProcessor.worklet.js',
				import.meta.url
			).toString();
			const monitorWorkletUrl = new URL('./monitor.worklet.js', import.meta.url).toString();
			await this.outputAudioContext!.audioWorklet.addModule(audioPlayerWorkletUrl);
			await this.outputAudioContext!.audioWorklet.addModule(monitorWorkletUrl);

			// Create main audio worklet
			this.playerWorkletNode = new AudioWorkletNode(
				this.outputAudioContext!,
				'audio-player-processor'
			);

			// Create monitor worklet to replace ScriptProcessorNode
			this.monitorWorkletNode = new AudioWorkletNode(
				this.outputAudioContext!,
				'audio-monitor-processor',
				{
					numberOfInputs: 1,
					numberOfOutputs: 1,
					channelCount: 1,
					channelCountMode: 'explicit',
					channelInterpretation: 'speakers'
				}
			);
			// Handle messages from monitor worklet
			this.monitorWorkletNode.port.onmessage = (event) => {
				const { type, audioData } = event.data;
				if (type === 'audio-played') {
					// Notify listeners that audio was played
					const samples = new Float32Array(audioData);
					this.onAudioPlayedListeners.forEach((listener) => listener(samples));
				}
			};

			// Connect audio graph: worklet -> monitor -> analyser -> destination
			this.playerWorkletNode.connect(this.monitorWorkletNode);
			this.monitorWorkletNode.connect(this.outputAnalyser);
			this.outputAnalyser!.connect(this.outputAudioContext!.destination);

			this.#maybeOverrideInitialBufferLength();

			//this.addEventListener('audioPlayed', () => {
			//	this.onAudioPlayedListeners.forEach((listener) => listener());
			//});

			this.outputInitialized = true;
			this.outputStarted = true;
		} catch (error: any) {
			console.error('Error starting output:', error);
		}
	}

	async startOutput() {
		try {
			this.outputAudioContext = new AudioContext({ sampleRate: 24000 });
			this.outputAnalyser = this.outputAudioContext!.createAnalyser();
			this.outputAnalyser.fftSize = 512;
			// Load both worklet modules
			const audioPlayerWorkletUrl = new URL(
				'./AudioPlayerProcessor.worklet.js',
				import.meta.url
			).toString();
			const monitorWorkletUrl = new URL('./monitor.worklet.js', import.meta.url).toString();
			await this.outputAudioContext!.audioWorklet.addModule(audioPlayerWorkletUrl);
			await this.outputAudioContext!.audioWorklet.addModule(monitorWorkletUrl);

			// Create main audio worklet
			this.playerWorkletNode = new AudioWorkletNode(
				this.outputAudioContext!,
				'audio-player-processor'
			);

			// Create monitor worklet to replace ScriptProcessorNode
			this.monitorWorkletNode = new AudioWorkletNode(
				this.outputAudioContext!,
				'audio-monitor-processor',
				{
					numberOfInputs: 1,
					numberOfOutputs: 1,
					channelCount: 1,
					channelCountMode: 'explicit',
					channelInterpretation: 'speakers'
				}
			);
			// Handle messages from monitor worklet
			this.monitorWorkletNode.port.onmessage = (event) => {
				const { type, audioData } = event.data;
				if (type === 'audio-played') {
					// Notify listeners that audio was played
					const samples = new Float32Array(audioData);
					this.onAudioPlayedListeners.forEach((listener) => listener(samples));
				}
			};

			// Connect audio graph: worklet -> monitor -> analyser -> destination
			this.playerWorkletNode.connect(this.monitorWorkletNode);
			this.monitorWorkletNode.connect(this.outputAnalyser);
			this.outputAnalyser!.connect(this.outputAudioContext!.destination);

			this.#maybeOverrideInitialBufferLength();

			//this.addEventListener('audioPlayed', () => {
			//	this.onAudioPlayedListeners.forEach((listener) => listener());
			//});

			this.outputInitialized = true;
			this.outputStarted = true;
		} catch (error: any) {
			console.error('Error starting output:', error);
		}
		return true;
	}

	public addEventListener(event: any, callback: any) {
		switch (event) {
			case 'onAudioPlayed':
				this.onAudioPlayedListeners.push(callback);
				break;
			default:
				console.error(
					'Listener registered for event type: ' + JSON.stringify(event) + ' which is not supported'
				);
		}
	}

	public bargeIn() {
		if (this.playerWorkletNode) {
			this.playerWorkletNode.port.postMessage({
				type: 'barge-in'
			});
		}
	}

	public stopOutput() {
		if (this.outputAudioContext) {
			this.outputAudioContext?.close();
		}

		if (this.outputAnalyser) {
			this.outputAnalyser?.disconnect();
		}

		if (this.playerWorkletNode) {
			this.playerWorkletNode?.disconnect();
		}

		if (this.monitorWorkletNode) {
			this.monitorWorkletNode?.disconnect();
		}

		this.outputInitialized = false;
		this.outputStarted = false;
		this.outputAudioContext = null;
		this.outputAnalyser = null;
		this.playerWorkletNode = null;
		this.monitorWorkletNode = null;
	}

	// # means really private at runtime
	#maybeOverrideInitialBufferLength() {
		// Read a user-specified initial buffer length from the URL parameters to help with tinkering
		const params = new URLSearchParams(window.location.search);
		const value = params.get('audioPlayerInitialBufferLength');
		if (value === null) {
			return; // No override specified
		}
		const bufferLength = parseInt(value);
		if (isNaN(bufferLength)) {
			console.error('Invalid audioPlayerInitialBufferLength value:', JSON.stringify(value));
			return;
		}
		this.playerWorkletNode?.port.postMessage({
			type: 'initial-buffer-length',
			bufferLength: bufferLength
		});
	}

	public playAudio(samples: any) {
		if (!this.outputInitialized) {
			console.error(
				'The audio player is not initialized. Call start() before attempting to play audio.'
			);
			return;
		}
		this.playerWorkletNode?.port.postMessage({
			type: 'audio',
			audioData: samples
		});
	}

	// ***************** INPUT ****************

	public async InitInput(sendAudioInput: (base64Data: string) => void) {
		try {
			//setStatus('Requesting microphone access...', 'info', Infinity);

			// Request microphone access
			this.inputStream = await navigator.mediaDevices.getUserMedia({
				audio: {
					echoCancellation: true,
					noiseSuppression: true,
					autoGainControl: true
				}
			});

			if (this.isFirefox) {
				//firefox doesn't allow audio context have differnt sample rate than what the user media device offers
				this.inputAudioContext = new AudioContext();
			} else {
				this.inputAudioContext = new AudioContext({
					sampleRate: this.TARGET_SAMPLE_RATE
				});
			}

			//samplingRatio - is only relevant for firefox, for Chromium based browsers, it's always 1
			this.samplingRatio = this.inputAudioContext.sampleRate / this.TARGET_SAMPLE_RATE;
			console.log(
				`Debug AudioContext- sampleRate: ${this.inputAudioContext.sampleRate} samplingRatio: ${this.samplingRatio}`
			);

			// Load microphone worklet
			const MicrophoneWorkletUrl = new URL('./microphone.worklet.js', import.meta.url).toString();
			await this.inputAudioContext.audioWorklet.addModule(MicrophoneWorkletUrl);

			// Create audio source
			this.sourceNode = this.inputAudioContext.createMediaStreamSource(this.inputStream);

			// Create modern AudioWorkletNode instead of deprecated ScriptProcessorNode
			this.microphoneWorkletNode = new AudioWorkletNode(
				this.inputAudioContext,
				'microphone-processor',
				{
					numberOfInputs: 1,
					numberOfOutputs: 0, // No output needed for microphone processing
					channelCount: 1,
					channelCountMode: 'explicit',
					channelInterpretation: 'speakers'
				}
			);

			/*
			// Handle messages from worklet
			this.microphoneWorkletNode.port.onmessage = (event) => {
				const { type, data } = event.data;
				if (type === 'audioData') {
					// Convert to base64 (browser-safe way)
					const base64Data = arrayBufferToBase64(data);

					// Send to server
					//socket.emit('audioInput', base64Data);
					sendAudioInput(base64Data);
				}
			};
			*/

			// Create gain node for input volume control
			//this.inputGainNode = this.inputAudioContext.createGain();
			// Configure worklet
			this.microphoneWorkletNode.port.postMessage({
				type: 'updateConfig',
				data: {
					isStreaming: true,
					samplingRatio: this.samplingRatio,
					isFirefox: this.isFirefox
				}
			});

			// Connect audio graph
			//this.sourceNode.connect(this.microphoneWorkletNode);

			// add the dashboard components
			this.inputGainNode = this.inputAudioContext.createGain();
			// Set initial gain
			this.inputGainNode.gain.value = this.inputGain;
			this.inputAnalyserNode = this.inputAudioContext.createAnalyser();
			// Configure analyser
			this.inputAnalyserNode.fftSize = 2048;
			this.inputAnalyserNode.smoothingTimeConstant = 0.8;

			this.sourceNode.connect(this.inputGainNode);
			this.inputGainNode.connect(this.inputAnalyserNode);
			this.inputAnalyserNode.connect(this.microphoneWorkletNode);
			// Start monitoring
			//this.startMetering();

			//setStatus('Microphone ready. Click Start to begin.', 'success', 30000);
			//startButtonEnabled = true;
			//await this.inputAudioContext?.suspend();
			this.inputInitialized = true;
		} catch (error: any) {
			console.error('Error accessing microphone:', error);
			//setStatus('Error: ' + error.message, 'error', Infinity);
		}
	}

	async startInput(sendAudioInput: (base64Data: string) => void) {
		// First, make sure the session is initialized
		//if (!this.inputInitialized) {
		//	await this.InitInput();
		//}
		if (this.inputAudioContext && this.inputAudioContext.state === 'suspended') {
			await this.inputAudioContext?.resume();
			this.inputStarted = true;
		}

		// Handle messages from worklet
		this.microphoneWorkletNode!.port.onmessage = (event) => {
			const { type, data } = event.data;
			if (type === 'audioData') {
				// Convert to base64 (browser-safe way)
				const base64Data = arrayBufferToBase64(data);

				// Send to server
				//socket.emit('audioInput', base64Data);
				sendAudioInput(base64Data);
			}
		};
	}

	// ***************** Audio manager ****************

	

	async requestPermissions(): Promise<boolean> {
		// Only work in browser environment
		if (typeof window === 'undefined' || !navigator?.mediaDevices) {
			return false;
		}

		try {
			const stream = await navigator.mediaDevices.getUserMedia({
				audio: true,
				video: false
			});
			stream.getTracks().forEach((track) => track.stop());
			await this.initDevices();
			return true;
		} catch (error) {
			console.error('Failed to request permissions:', error);
			return false;
		}
	}

	stopAudioInput(): void {
		if (typeof window !== 'undefined' && this.animationFrame) {
			cancelAnimationFrame(this.animationFrame);
			this.animationFrame = null;
		}

		if (this.inputStream) {
			this.inputStream.getTracks().forEach((track) => track.stop());
			this.inputStream = null;
		}

		if (this.inputNode) {
			this.inputNode.disconnect();
			this.inputNode = null;
		}

		if (this.gainNode) {
			this.gainNode.disconnect();
			this.gainNode = null;
		}

		if (this.inputAnalyserNode) {
			this.inputAnalyserNode.disconnect();
			this.inputAnalyserNode = null;
		}

		if (this.destinationNode) {
			this.destinationNode.disconnect();
			this.destinationNode = null;
		}
	}

	private startMetering(): void {
		const bufferLength = this.inputAnalyserNode!.frequencyBinCount;
		const dataArray = new Uint8Array(bufferLength);
		const floatArray = new Float32Array(bufferLength);

		const updateMeter = () => {
			if (!this.inputAnalyserNode) return;

			this.inputAnalyserNode.getByteFrequencyData(dataArray);
			this.inputAnalyserNode.getFloatFrequencyData(floatArray);

			// Calculate RMS and peak
			let sum = 0;
			let peak = 0;

			for (let i = 0; i < bufferLength; i++) {
				const value = dataArray[i] / 255;
				sum += value * value;
				peak = Math.max(peak, value);
			}

			const rms = Math.sqrt(sum / bufferLength);
			const level = rms;
			const clipping = peak > 0.95;

			const meterData: AudioMeterData = {
				level,
				peak,
				rms,
				clipping
			};

			this.meterCallbacks.forEach((callback) => callback(meterData));
			this.animationFrame = requestAnimationFrame(updateMeter);
		};

		updateMeter();
	}

	
	onMeterUpdate(callback: (data: AudioMeterData) => void): () => void {
		if (this.meterCallbacks) {
			this.meterCallbacks.push(callback);

			return () => {
				const index = this.meterCallbacks.indexOf(callback);
				if (index > -1) {
					this.meterCallbacks.splice(index, 1);
				}
			};
		}
		return () => {};
	}

	onDeviceChange(callback: (devices: AudioDevice[]) => void): () => void {
		this.deviceChangeCallbacks.push(callback);
		return () => {
			const index = this.deviceChangeCallbacks.indexOf(callback);
			if (index > -1) {
				this.deviceChangeCallbacks.splice(index, 1);
			}
		};
	}

	async destroy(): Promise<void> {
		this.stopAudioInput();

		if (this.inputAudioContext) {
			await this.inputAudioContext.close();
			this.inputAudioContext = null;
		}

		if (this.outputAudioContext) {
			await this.outputAudioContext.close();
			this.outputAudioContext = null;
		}

		this.meterCallbacks.length = 0;
		this.deviceChangeCallbacks.length = 0;
	}
}
