<script lang="ts">
	import { onMount } from 'svelte';
	import type { AudioDevice } from './types';
	import * as Select from '$lib/components/ui/select/index.js';

	interface Props {
		devices: AudioDevice[];
		type: 'input' | 'output';
		selectedDeviceId: string;
	}

	let { devices = $bindable(),type, selectedDeviceId = $bindable('default') }: Props = $props();

	

	let selectedDevice = $derived(
		devices.find((d) => d.deviceId === selectedDeviceId) || {
			deviceId: 'default',
			label: `Default ${type === 'input' ? 'Input' : 'Output'}`,
			kind: type === 'input' ? ('audioinput' as const) : ('audiooutput' as const),
			groupId: ''
		}
	);

</script>

<div class="device-selector relative w-full">

	<Select.Root type="single" name="selectedDeviceId" bind:value={selectedDeviceId}>
		<Select.Trigger class="text-xs">{selectedDevice.label.substring(0, 20)}</Select.Trigger>
		<Select.Content>
			{#if devices.length === 0}
				<div class="text-audio-muted dark:text-audio-muted px-3 py-2 text-xs">
					No {type === 'input' ? 'input' : 'output'} devices found
				</div>
			{:else}
				{#each devices as device (device.deviceId)}
					<Select.Item class="text-xs"
					value={device.deviceId}>{device.label}</Select.Item>
				{/each}
			{/if}
		</Select.Content>
	</Select.Root>
</div>
